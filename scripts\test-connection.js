#!/usr/bin/env node

/**
 * سكريبت اختبار الاتصال بقاعدة البيانات
 * يمكن استخدامه للتحقق من صحة إعدادات قاعدة البيانات قبل تشغيل التطبيق
 */

const sql = require('mssql');
require('dotenv').config({ path: '.env.local' });

const config = {
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_DATABASE || 'test',
  user: process.env.DB_USERNAME || 'sa',
  password: process.env.DB_PASSWORD || '',
  port: parseInt(process.env.DB_PORT || '1433'),
  options: {
    encrypt: false,
    trustServerCertificate: process.env.DB_TRUST_CERTIFICATE === 'true'
  }
};

async function testConnection() {
  console.log('🔍 اختبار الاتصال بقاعدة البيانات...');
  console.log(`📍 الخادم: ${config.server}:${config.port}`);
  console.log(`🗄️  قاعدة البيانات: ${config.database}`);
  console.log(`👤 المستخدم: ${config.user}`);
  console.log('');

  let pool;
  
  try {
    // محاولة الاتصال
    console.log('⏳ جاري الاتصال...');
    pool = await sql.connect(config);
    console.log('✅ تم الاتصال بنجاح!');
    
    // اختبار استعلام بسيط
    console.log('⏳ اختبار استعلام بسيط...');
    const result = await pool.request().query('SELECT @@VERSION as version, GETDATE() as current_time');
    
    if (result.recordset && result.recordset.length > 0) {
      console.log('✅ تم تنفيذ الاستعلام بنجاح!');
      console.log(`📊 إصدار SQL Server: ${result.recordset[0].version.split('\n')[0]}`);
      console.log(`🕐 الوقت الحالي: ${result.recordset[0].current_time}`);
    }
    
    // اختبار الجداول المتاحة
    console.log('⏳ فحص الجداول المتاحة...');
    const tablesResult = await pool.request().query(`
      SELECT TABLE_NAME, TABLE_TYPE 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `);
    
    if (tablesResult.recordset && tablesResult.recordset.length > 0) {
      console.log(`✅ تم العثور على ${tablesResult.recordset.length} جدول:`);
      tablesResult.recordset.slice(0, 10).forEach((table, index) => {
        console.log(`   ${index + 1}. ${table.TABLE_NAME}`);
      });
      
      if (tablesResult.recordset.length > 10) {
        console.log(`   ... و ${tablesResult.recordset.length - 10} جدول آخر`);
      }
    } else {
      console.log('⚠️  لم يتم العثور على جداول في قاعدة البيانات');
    }
    
    console.log('');
    console.log('🎉 اختبار الاتصال مكتمل بنجاح!');
    console.log('💡 يمكنك الآن تشغيل التطبيق باستخدام: npm run dev');
    
  } catch (error) {
    console.error('❌ فشل في الاتصال بقاعدة البيانات:');
    console.error('');
    
    if (error.code === 'ELOGIN') {
      console.error('🔐 خطأ في المصادقة:');
      console.error('   - تحقق من اسم المستخدم وكلمة المرور');
      console.error('   - تأكد من أن المستخدم له صلاحيات الوصول لقاعدة البيانات');
    } else if (error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
      console.error('🌐 خطأ في الشبكة:');
      console.error('   - تحقق من عنوان الخادم ورقم المنفذ');
      console.error('   - تأكد من أن SQL Server يعمل ويقبل الاتصالات');
      console.error('   - تحقق من إعدادات الجدار الناري');
    } else if (error.code === 'EINSTLOOKUP') {
      console.error('🔍 خطأ في البحث عن المثيل:');
      console.error('   - تحقق من اسم مثيل SQL Server');
      console.error('   - تأكد من تشغيل خدمة SQL Server Browser');
    } else {
      console.error('❓ خطأ غير معروف:');
      console.error(`   الكود: ${error.code || 'غير محدد'}`);
      console.error(`   الرسالة: ${error.message}`);
    }
    
    console.error('');
    console.error('🛠️  نصائح لحل المشكلة:');
    console.error('   1. تحقق من ملف .env.local وتأكد من صحة البيانات');
    console.error('   2. تأكد من تشغيل SQL Server');
    console.error('   3. اختبر الاتصال باستخدام SQL Server Management Studio');
    console.error('   4. تحقق من إعدادات الشبكة والجدار الناري');
    
    process.exit(1);
  } finally {
    if (pool) {
      try {
        await pool.close();
        console.log('🔌 تم إغلاق الاتصال');
      } catch (closeError) {
        console.error('⚠️  خطأ في إغلاق الاتصال:', closeError.message);
      }
    }
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testConnection().catch(error => {
    console.error('💥 خطأ غير متوقع:', error);
    process.exit(1);
  });
}

module.exports = { testConnection, config };
