import { NextRequest, NextResponse } from 'next/server';
import sql from 'mssql';
import fs from 'fs/promises';
import path from 'path';
import { findCachedDatabase, addDatabaseToCache } from '@/lib/database-cache';

interface ConnectionData {
  server: string;
  database: string;
  username: string;
  password: string;
  port: string;
  trustServerCertificate: boolean;
}

interface TableSchema {
  name: string;
  description: string;
  columns: ColumnSchema[];
}

interface ColumnSchema {
  name: string;
  type: string;
  description: string;
  nullable: boolean;
  isPrimaryKey: boolean;
  isForeignKey: boolean;
}

export async function POST(request: NextRequest) {
  let pool: sql.ConnectionPool | null = null;

  try {
    const connectionData: ConnectionData = await request.json();

    // التحقق من وجود قاعدة البيانات في ذاكرة التخزين المؤقت
    const cachedDatabase = await findCachedDatabase(connectionData);

    if (cachedDatabase) {
      console.log('تم العثور على قاعدة البيانات في ذاكرة التخزين المؤقت');
      return NextResponse.json({
        success: true,
        message: 'تم تحميل قاعدة البيانات من ذاكرة التخزين المؤقت',
        schema: cachedDatabase.schema,
        tablesCount: cachedDatabase.schema.tables.length,
        columnsCount: cachedDatabase.schema.tables.reduce((sum: number, table: any) => sum + table.columns.length, 0),
        cached: true,
        indexed_at: cachedDatabase.indexed_at
      });
    }

    // إعداد تكوين الاتصال
    const config: sql.config = {
      server: connectionData.server,
      database: connectionData.database,
      port: parseInt(connectionData.port) || 1433,
      options: {
        encrypt: true,
        trustServerCertificate: connectionData.trustServerCertificate,
        enableArithAbort: true,
      },
    };

    if (connectionData.username && connectionData.password) {
      config.user = connectionData.username;
      config.password = connectionData.password;
    } else {
      config.authentication = {
        type: 'ntlm',
        options: {
          domain: '',
          userName: '',
          password: '',
        },
      };
    }

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // استعلام للحصول على معلومات الجداول والأعمدة
    const tablesQuery = `
      SELECT 
        t.TABLE_NAME,
        t.TABLE_TYPE,
        c.COLUMN_NAME,
        c.DATA_TYPE,
        c.IS_NULLABLE,
        c.COLUMN_DEFAULT,
        c.CHARACTER_MAXIMUM_LENGTH,
        c.NUMERIC_PRECISION,
        c.NUMERIC_SCALE,
        CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_PRIMARY_KEY,
        CASE WHEN fk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_FOREIGN_KEY
      FROM INFORMATION_SCHEMA.TABLES t
      LEFT JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
      LEFT JOIN (
        SELECT ku.TABLE_NAME, ku.COLUMN_NAME
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
        WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
      ) pk ON c.TABLE_NAME = pk.TABLE_NAME AND c.COLUMN_NAME = pk.COLUMN_NAME
      LEFT JOIN (
        SELECT ku.TABLE_NAME, ku.COLUMN_NAME
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
        WHERE tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
      ) fk ON c.TABLE_NAME = fk.TABLE_NAME AND c.COLUMN_NAME = fk.COLUMN_NAME
      WHERE t.TABLE_TYPE = 'BASE TABLE'
      ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION
    `;

    const result = await pool.request().query(tablesQuery);
    
    // تنظيم البيانات
    const tablesMap = new Map<string, TableSchema>();
    
    for (const row of result.recordset) {
      if (!tablesMap.has(row.TABLE_NAME)) {
        tablesMap.set(row.TABLE_NAME, {
          name: row.TABLE_NAME,
          description: `جدول ${row.TABLE_NAME}`,
          columns: []
        });
      }
      
      const table = tablesMap.get(row.TABLE_NAME)!;
      
      if (row.COLUMN_NAME) {
        const columnType = row.CHARACTER_MAXIMUM_LENGTH 
          ? `${row.DATA_TYPE}(${row.CHARACTER_MAXIMUM_LENGTH})`
          : row.NUMERIC_PRECISION 
          ? `${row.DATA_TYPE}(${row.NUMERIC_PRECISION},${row.NUMERIC_SCALE || 0})`
          : row.DATA_TYPE;

        table.columns.push({
          name: row.COLUMN_NAME,
          type: columnType,
          description: `عمود ${row.COLUMN_NAME} من نوع ${columnType}`,
          nullable: row.IS_NULLABLE === 'YES',
          isPrimaryKey: row.IS_PRIMARY_KEY === 1,
          isForeignKey: row.IS_FOREIGN_KEY === 1
        });
      }
    }

    const schema = {
      tables: Array.from(tablesMap.values())
    };

    // حفظ المخطط في ملف
    const schemaPath = path.join(process.cwd(), 'src/data/database-schema.json');
    await fs.writeFile(schemaPath, JSON.stringify(schema, null, 2), 'utf-8');

    // إنشاء ملف التوصيف الذكي لكل جدول مع التصنيف الذكي
    await generateTableProfiles(schema.tables);

    // إضافة التصنيف الذكي للأعمدة
    schema.intelligentClassification = await classifyColumnsIntelligently(schema.tables);

    await pool.close();

    return NextResponse.json({
      success: true,
      message: 'تم فهرسة قاعدة البيانات بنجاح',
      schema,
      tablesCount: schema.tables.length,
      columnsCount: schema.tables.reduce((sum, table) => sum + table.columns.length, 0)
    });

  } catch (error: any) {
    console.error('خطأ في فهرسة قاعدة البيانات:', error);
    
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {
        console.error('خطأ في إغلاق الاتصال:', closeError);
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: 'فشل في فهرسة قاعدة البيانات',
        details: error.message
      },
      { status: 500 }
    );
  }
}

async function generateTableProfiles(tables: TableSchema[]) {
  try {
    for (const table of tables) {
      const profile = {
        table: table.name,
        description: `جدول ${table.name} يحتوي على ${table.columns.length} عمود`,
        fields: {} as Record<string, string>,
        use_cases: generateUseCases(table)
      };

      // إنشاء وصف لكل عمود
      for (const column of table.columns) {
        profile.fields[column.name] = `${column.description}${column.isPrimaryKey ? ' (مفتاح أساسي)' : ''}${column.isForeignKey ? ' (مفتاح خارجي)' : ''}`;
      }

      // حفظ ملف التوصيف
      const profilePath = path.join(process.cwd(), `src/data/profiles/${table.name}-profile.json`);
      
      // إنشاء مجلد profiles إذا لم يكن موجوداً
      const profilesDir = path.dirname(profilePath);
      try {
        await fs.access(profilesDir);
      } catch {
        await fs.mkdir(profilesDir, { recursive: true });
      }
      
      await fs.writeFile(profilePath, JSON.stringify(profile, null, 2), 'utf-8');
    }
  } catch (error) {
    console.error('خطأ في إنشاء ملفات التوصيف:', error);
  }
}

// التصنيف الذكي للأعمدة حسب الفئات المطلوبة
async function classifyColumnsIntelligently(tables: TableSchema[]) {
  const classification = {
    المنتجات: {
      description: "أي شيء يخص الصنف والكمية والسعر والوحدة",
      columns: [],
      tables: []
    },
    العملاء: {
      description: "هوية العميل والموزع وسجل الشراء",
      columns: [],
      tables: []
    },
    المخازن: {
      description: "المستودعات، الفروع، والمخزون",
      columns: [],
      tables: []
    },
    المبالغ: {
      description: "كل ما يتعلق بالقيمة، السعر، الخصومات",
      columns: [],
      tables: []
    },
    التواريخ: {
      description: "لتصفية الفترات وتحليل الأداء",
      columns: [],
      tables: []
    },
    الفواتير: {
      description: "المستندات والرقم التسلسلي",
      columns: [],
      tables: []
    },
    المحاسبة: {
      description: "الحسابات ومراكز التكلفة",
      columns: [],
      tables: []
    }
  };

  for (const table of tables) {
    for (const column of table.columns) {
      const columnName = column.name.toLowerCase();
      const columnType = column.type.toLowerCase();

      // تصنيف المنتجات
      if (columnName.includes('item') || columnName.includes('product') ||
          columnName.includes('quantity') || columnName.includes('unit') ||
          columnName.includes('category') || columnName.includes('barcode') ||
          columnName.includes('expiry')) {
        classification.المنتجات.columns.push(`${table.name}.${column.name}`);
      }

      // تصنيف العملاء
      else if (columnName.includes('client') || columnName.includes('customer') ||
               columnName.includes('distributor') || columnName.includes('supplier')) {
        classification.العملاء.columns.push(`${table.name}.${column.name}`);
      }

      // تصنيف المخازن
      else if (columnName.includes('branch') || columnName.includes('store') ||
               columnName.includes('warehouse') || columnName.includes('stock')) {
        classification.المخازن.columns.push(`${table.name}.${column.name}`);
      }

      // تصنيف المبالغ
      else if (columnName.includes('amount') || columnName.includes('price') ||
               columnName.includes('total') || columnName.includes('discount') ||
               columnName.includes('currency') || columnName.includes('exchange')) {
        classification.المبالغ.columns.push(`${table.name}.${column.name}`);
      }

      // تصنيف التواريخ
      else if (columnType.includes('date') || columnType.includes('time') ||
               columnName.includes('date') || columnName.includes('time') ||
               columnName.includes('year')) {
        classification.التواريخ.columns.push(`${table.name}.${column.name}`);
      }

      // تصنيف الفواتير
      else if (columnName.includes('document') || columnName.includes('invoice') ||
               columnName.includes('record') || columnName.includes('serial') ||
               columnName.includes('number')) {
        classification.الفواتير.columns.push(`${table.name}.${column.name}`);
      }

      // تصنيف المحاسبة
      else if (columnName.includes('account') || columnName.includes('cost') ||
               columnName.includes('center')) {
        classification.المحاسبة.columns.push(`${table.name}.${column.name}`);
      }
    }

    // تصنيف الجداول حسب محتواها
    const tableName = table.name.toLowerCase();
    if (tableName.includes('item') || tableName.includes('product')) {
      classification.المنتجات.tables.push(table.name);
    } else if (tableName.includes('client') || tableName.includes('customer')) {
      classification.العملاء.tables.push(table.name);
    } else if (tableName.includes('invoice') || tableName.includes('document')) {
      classification.الفواتير.tables.push(table.name);
    } else {
      // تصنيف عام للجداول الرئيسية
      classification.المنتجات.tables.push(table.name);
      classification.العملاء.tables.push(table.name);
      classification.المخازن.tables.push(table.name);
      classification.المبالغ.tables.push(table.name);
      classification.التواريخ.tables.push(table.name);
      classification.الفواتير.tables.push(table.name);
    }
  }

  return classification;
}

function generateUseCases(table: TableSchema) {
  const useCases = [];

  // البحث عن أعمدة مهمة
  const nameColumns = table.columns.filter(col =>
    col.name.toLowerCase().includes('name') ||
    col.name.toLowerCase().includes('اسم') ||
    col.name.toLowerCase().includes('item')
  );

  const amountColumns = table.columns.filter(col =>
    col.name.toLowerCase().includes('amount') ||
    col.name.toLowerCase().includes('مبلغ') ||
    col.name.toLowerCase().includes('price') ||
    col.name.toLowerCase().includes('سعر') ||
    col.name.toLowerCase().includes('total')
  );

  const dateColumns = table.columns.filter(col =>
    col.type.toLowerCase().includes('date') ||
    col.type.toLowerCase().includes('time') ||
    col.name.toLowerCase().includes('date')
  );

  const quantityColumns = table.columns.filter(col =>
    col.name.toLowerCase().includes('quantity') ||
    col.name.toLowerCase().includes('كمية')
  );

  // إنشاء حالات استخدام متقدمة
  if (nameColumns.length > 0 && quantityColumns.length > 0) {
    useCases.push({
      name: `أكثر ${nameColumns[0].name} مبيعاً`,
      description: `استعلام لعرض أكثر العناصر في ${nameColumns[0].name} مبيعاً حسب ${quantityColumns[0].name}`,
      example_sql: `SELECT TOP 10 ${nameColumns[0].name}, SUM(${quantityColumns[0].name}) as TotalQuantity FROM ${table.name} WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ${nameColumns[0].name} ORDER BY TotalQuantity DESC`
    });
  }

  if (nameColumns.length > 0 && amountColumns.length > 0) {
    useCases.push({
      name: `أعلى ${nameColumns[0].name} حسب ${amountColumns[0].name}`,
      description: `استعلام لعرض أعلى القيم في ${nameColumns[0].name} مرتبة حسب ${amountColumns[0].name}`,
      example_sql: `SELECT TOP 10 ${nameColumns[0].name}, SUM(${amountColumns[0].name}) as Total FROM ${table.name} GROUP BY ${nameColumns[0].name} ORDER BY Total DESC`
    });
  }

  if (dateColumns.length > 0 && amountColumns.length > 0) {
    useCases.push({
      name: `تحليل زمني لـ ${amountColumns[0].name}`,
      description: `استعلام لتحليل ${amountColumns[0].name} عبر الزمن`,
      example_sql: `SELECT YEAR(${dateColumns[0].name}) as Year, MONTH(${dateColumns[0].name}) as Month, SUM(${amountColumns[0].name}) as Total FROM ${table.name} GROUP BY YEAR(${dateColumns[0].name}), MONTH(${dateColumns[0].name}) ORDER BY Year, Month`
    });
  }

  // إضافة حالات استخدام للعملاء والفروع
  const clientColumns = table.columns.filter(col =>
    col.name.toLowerCase().includes('client') ||
    col.name.toLowerCase().includes('customer')
  );

  const branchColumns = table.columns.filter(col =>
    col.name.toLowerCase().includes('branch') ||
    col.name.toLowerCase().includes('store')
  );

  if (clientColumns.length > 0 && amountColumns.length > 0) {
    useCases.push({
      name: `أكثر العملاء شراءً`,
      description: `استعلام لعرض أكثر العملاء شراءً حسب ${amountColumns[0].name}`,
      example_sql: `SELECT TOP 10 ${clientColumns[0].name}, SUM(${amountColumns[0].name}) as TotalPurchases FROM ${table.name} WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ${clientColumns[0].name} ORDER BY TotalPurchases DESC`
    });
  }

  if (branchColumns.length > 0 && amountColumns.length > 0) {
    useCases.push({
      name: `أداء الفروع`,
      description: `استعلام لمقارنة أداء الفروع حسب ${amountColumns[0].name}`,
      example_sql: `SELECT ${branchColumns[0].name}, SUM(${amountColumns[0].name}) as TotalSales FROM ${table.name} WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ${branchColumns[0].name} ORDER BY TotalSales DESC`
    });
  }

  return useCases;
}
