import { NextRequest, NextResponse } from 'next/server';
import sql from 'mssql';
import { analyzeQuery, generateSQL, analyzeResults } from '@/lib/ai-service';

interface ConnectionData {
  server: string;
  database: string;
  username: string;
  password: string;
  port: string;
  trustServerCertificate: boolean;
}

export async function POST(request: NextRequest) {
  let pool: sql.ConnectionPool | null = null;
  
  try {
    const { query, connectionData }: { query: string; connectionData: ConnectionData } = await request.json();

    if (!query || !connectionData) {
      return NextResponse.json(
        { error: 'الاستعلام وبيانات الاتصال مطلوبة' },
        { status: 400 }
      );
    }

    // تحليل الاستعلام واستخراج النية والكيانات
    const queryAnalysis = await analyzeQuery(query);
    
    if (!queryAnalysis.success) {
      return NextResponse.json(
        { error: 'فشل في تحليل الاستعلام' },
        { status: 400 }
      );
    }

    // توليد استعلام SQL
    const sqlGeneration = await generateSQL(queryAnalysis);
    
    if (!sqlGeneration.success || !sqlGeneration.sql) {
      return NextResponse.json(
        { error: 'فشل في توليد استعلام SQL' },
        { status: 400 }
      );
    }

    // إعداد الاتصال بقاعدة البيانات
    const config: sql.config = {
      server: connectionData.server,
      database: connectionData.database,
      port: parseInt(connectionData.port) || 1433,
      options: {
        encrypt: true,
        trustServerCertificate: connectionData.trustServerCertificate,
        enableArithAbort: true,
      },
    };

    if (connectionData.username && connectionData.password) {
      config.user = connectionData.username;
      config.password = connectionData.password;
    } else {
      config.authentication = {
        type: 'ntlm',
        options: {
          domain: '',
          userName: '',
          password: '',
        },
      };
    }

    // تنفيذ الاستعلام
    pool = await sql.connect(config);
    const result = await pool.request().query(sqlGeneration.sql);
    
    // تحليل النتائج
    const analysis = await analyzeResults(query, result.recordset, queryAnalysis);

    await pool.close();

    return NextResponse.json({
      success: true,
      query,
      sql: sqlGeneration.sql,
      data: result.recordset,
      analysis: analysis.insights,
      visualization: queryAnalysis.visualization,
      intent: queryAnalysis.intent,
      entities: queryAnalysis.entities
    });

  } catch (error: any) {
    console.error('خطأ في معالجة الاستعلام:', error);
    
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {
        console.error('خطأ في إغلاق الاتصال:', closeError);
      }
    }

    let errorMessage = 'فشل في معالجة الاستعلام';
    
    if (error.message?.includes('Invalid object name')) {
      errorMessage = 'اسم الجدول أو العمود غير صحيح';
    } else if (error.message?.includes('Syntax error')) {
      errorMessage = 'خطأ في صيغة الاستعلام';
    } else if (error.message?.includes('Permission denied')) {
      errorMessage = 'ليس لديك صلاحية لتنفيذ هذا الاستعلام';
    } else if (error.message) {
      errorMessage = error.message;
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        details: error.code || 'UNKNOWN_ERROR'
      },
      { status: 500 }
    );
  }
}
