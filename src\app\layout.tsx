import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "وكيل الذكاء الاصطناعي لـ SQL",
  description: "نظام ذكي لتحليل البيانات والاستعلام عن قواعد البيانات باللغة الطبيعية",
  keywords: ["SQL", "AI", "Database", "Analytics", "Arabic"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${inter.variable} font-sans antialiased`}>
        {children}
      </body>
    </html>
  );
}
