'use client';

import { useState, useEffect } from 'react';
import { TrendingUp, Users, Package, DollarSign, Calendar, Target, AlertTriangle, Lightbulb } from 'lucide-react';

interface QueryResult {
  id: string;
  query: string;
  sql: string;
  data: any[];
  analysis: string;
  timestamp: Date;
  visualization?: string;
}

interface AnalysisPanelProps {
  queryHistory: QueryResult[];
  connectionData: any;
}

interface Insight {
  type: 'trend' | 'pattern' | 'anomaly' | 'recommendation';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: string;
}

export default function AnalysisPanel({ queryHistory, connectionData }: AnalysisPanelProps) {
  const [insights, setInsights] = useState<Insight[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { id: 'all', name: 'جميع التحليلات', icon: Target },
    { id: 'sales', name: 'المبيعات', icon: TrendingUp },
    { id: 'customers', name: 'العملاء', icon: Users },
    { id: 'products', name: 'المنتجات', icon: Package },
    { id: 'financial', name: 'المالي', icon: DollarSign },
    { id: 'temporal', name: 'الزمني', icon: Calendar }
  ];

  useEffect(() => {
    if (queryHistory.length > 0) {
      generateInsights();
    }
  }, [queryHistory]);

  const generateInsights = async () => {
    setIsAnalyzing(true);
    
    try {
      const response = await fetch('/api/analysis/generate-insights', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          queryHistory,
          connectionData
        }),
      });

      const result = await response.json();
      
      if (response.ok) {
        setInsights(result.insights);
      }
    } catch (error) {
      console.error('خطأ في توليد التحليلات:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'trend':
        return TrendingUp;
      case 'pattern':
        return Target;
      case 'anomaly':
        return AlertTriangle;
      case 'recommendation':
        return Lightbulb;
      default:
        return Target;
    }
  };

  const getInsightColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';
      case 'low':
        return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';
      default:
        return 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/20';
    }
  };

  const filteredInsights = selectedCategory === 'all' 
    ? insights 
    : insights.filter(insight => insight.category === selectedCategory);

  if (queryHistory.length === 0) {
    return (
      <div className="text-center py-12">
        <Target className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          لا توجد تحليلات متاحة
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          قم بتنفيذ بعض الاستعلامات أولاً لرؤية التحليلات والرؤى الذكية
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Category Filter */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          التحليلات والرؤى الذكية
        </h2>
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => {
            const Icon = category.icon;
            return (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {category.name}
              </button>
            );
          })}
        </div>
      </div>

      {/* Insights Grid */}
      {isAnalyzing ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">جاري تحليل البيانات وتوليد الرؤى...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredInsights.map((insight, index) => {
            const Icon = getInsightIcon(insight.type);
            return (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border-r-4 border-blue-500"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-lg ${getInsightColor(insight.impact)}`}>
                      <Icon className="w-5 h-5" />
                    </div>
                    <div className="mr-3">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {insight.title}
                      </h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getInsightColor(insight.impact)}`}>
                        {insight.impact === 'high' ? 'تأثير عالي' : 
                         insight.impact === 'medium' ? 'تأثير متوسط' : 'تأثير منخفض'}
                      </span>
                    </div>
                  </div>
                </div>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  {insight.description}
                </p>
              </div>
            );
          })}
        </div>
      )}

      {/* Summary Statistics */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          ملخص الإحصائيات
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {queryHistory.length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              إجمالي الاستعلامات
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {insights.filter(i => i.type === 'trend').length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              الاتجاهات المكتشفة
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
              {insights.filter(i => i.type === 'anomaly').length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              الشذوذات المكتشفة
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {insights.filter(i => i.type === 'recommendation').length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              التوصيات
            </div>
          </div>
        </div>
      </div>

      {/* Action Items */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          إجراءات مقترحة
        </h3>
        <div className="space-y-3">
          {insights
            .filter(insight => insight.type === 'recommendation')
            .slice(0, 5)
            .map((recommendation, index) => (
              <div
                key={index}
                className="flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
              >
                <Lightbulb className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3" />
                <span className="text-gray-700 dark:text-gray-300">
                  {recommendation.description}
                </span>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
}
