{"intents": [{"name": "GET_PRODUCT_DETAILS", "description": "عرض تفاصيل منتج محدد", "patterns": ["أعرض لي تفاصيل المنتج {product_name}", "معلومات عن {product_name}", "بيانات المنتج {product_name}", "تفاصيل {product_name}", "ما هي معلومات {product_name}"], "required_entities": ["product_name"], "optional_entities": ["time_period", "branch_name"], "suggested_tables": ["tbltemp_ItemsMain"], "suggested_columns": ["ItemName", "UnitPrice", "Quantity", "Amount", "TheDate", "BranchName"], "response_type": "detailed_info"}, {"name": "COMPARE_PRODUCTS", "description": "مقارنة بين منتجين أو أكثر", "patterns": ["قارن بين {product1} و {product2}", "مقارنة المنتجات {product_list}", "أيهما أفضل {product1} أم {product2}", "الفرق بين {product1} و {product2}", "مقارنة {product1} مع {product2}"], "required_entities": ["product1", "product2"], "optional_entities": ["time_period", "metric", "branch_name"], "suggested_tables": ["tbltemp_ItemsMain"], "suggested_columns": ["ItemName", "Quantity", "Amount", "UnitPrice", "TheDate"], "response_type": "comparison"}, {"name": "TOP_SELLING_PRODUCTS", "description": "أعلى المنتجات مبيعاً", "patterns": ["أعلى {number} منتجات مبيعاً", "أكثر المنتجات مبيعاً في {time_period}", "المنتجات الأكثر مبيعاً", "أفضل المنتجات مبيعاً", "ترتيب المنتجات حسب المبيعات"], "required_entities": [], "optional_entities": ["number", "time_period", "branch_name"], "suggested_tables": ["tbltemp_ItemsMain"], "suggested_columns": ["ItemName", "Quantity", "Amount", "TheDate", "BranchName"], "response_type": "ranking"}, {"name": "SALES_TREND_ANALYSIS", "description": "تحليل اتجاهات المبيعات", "patterns": ["اتجاهات مبيعات {product_name}", "تحليل مبيعات {time_period}", "نمو المبيعات لـ {entity}", "تطور المبيعات", "اتجاه المبيعات الشهرية"], "required_entities": [], "optional_entities": ["product_name", "time_period", "branch_name"], "suggested_tables": ["tbltemp_ItemsMain"], "suggested_columns": ["TheDate", "Amount", "Quantity", "ItemName", "BranchName"], "response_type": "trend_analysis"}, {"name": "CLIENT_ANALYSIS", "description": "تحليل العملاء", "patterns": ["أكثر العملاء شراءً", "تحليل العميل {client_name}", "عملاء الفرع {branch_name}", "أفضل العملاء", "ترتيب العملاء حسب المشتريات"], "required_entities": [], "optional_entities": ["client_name", "branch_name", "time_period", "number"], "suggested_tables": ["tbltemp_ItemsMain"], "suggested_columns": ["ClientName", "Amount", "Quantity", "BranchName", "TheDate"], "response_type": "client_analysis"}, {"name": "BRANCH_ANALYSIS", "description": "تحليل الفروع", "patterns": ["أداء الفروع", "مقارنة الفروع", "أفضل الفروع مبيعاً", "تحليل الفرع {branch_name}", "مبيعات الفرع {branch_name}"], "required_entities": [], "optional_entities": ["branch_name", "time_period", "number"], "suggested_tables": ["tbltemp_ItemsMain"], "suggested_columns": ["BranchName", "Amount", "Quantity", "TheDate", "ItemName"], "response_type": "branch_analysis"}, {"name": "FINANCIAL_ANALYSIS", "description": "التحليل المالي", "patterns": ["إجمالي المبيعات", "الأرباح في {time_period}", "التحليل المالي", "المبيعات والأرباح", "الإيرادات"], "required_entities": [], "optional_entities": ["time_period", "branch_name"], "suggested_tables": ["tbltemp_ItemsMain"], "suggested_columns": ["Amount", "UnitPrice", "Quantity", "TheDate", "BranchName"], "response_type": "financial_analysis"}, {"name": "INVENTORY_ANALYSIS", "description": "تحليل المخزون", "patterns": ["حالة المخزون", "المنتجات المتوفرة", "تحليل المخزون", "الكميات المتاحة", "مخزون {product_name}"], "required_entities": [], "optional_entities": ["product_name", "branch_name"], "suggested_tables": ["tbltemp_ItemsMain"], "suggested_columns": ["ItemName", "Quantity", "BranchName", "TheDate"], "response_type": "inventory_analysis"}, {"name": "TIME_BASED_ANALYSIS", "description": "التحليل الزمني", "patterns": ["مبيعات {time_period}", "أداء {time_period}", "مقارنة {time_period1} مع {time_period2}", "نمو المبيعات السنوي", "التحليل الشهري"], "required_entities": ["time_period"], "optional_entities": ["time_period2", "branch_name", "product_name"], "suggested_tables": ["tbltemp_ItemsMain"], "suggested_columns": ["TheDate", "Amount", "Quantity", "ItemName", "BranchName"], "response_type": "time_analysis"}, {"name": "DOCUMENT_ANALYSIS", "description": "تحليل المستندات والفواتير", "patterns": ["تحليل الفواتير", "أنواع المستندات", "فواتير {document_type}", "تحليل {document_type}", "إحصائيات الفواتير"], "required_entities": [], "optional_entities": ["document_type", "time_period", "branch_name"], "suggested_tables": ["tbltemp_ItemsMain"], "suggested_columns": ["DocumentName", "Amount", "Quantity", "TheDate", "BranchName"], "response_type": "document_analysis"}]}