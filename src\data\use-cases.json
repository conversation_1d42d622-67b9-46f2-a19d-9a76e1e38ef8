{"useCases": [{"name": "أكثر المنتجات مبيعاً", "description": "عرض أكثر المنتجات مبيعاً خلال فترة معينة", "category": "تحليل المنتجات", "example_queries": ["أعرض لي أكثر 10 منتجات مبيعاً", "ما هي أكثر المنتجات مبيعاً هذا الشهر؟"], "sql_template": "SELECT TOP {number} ItemName, SUM(Quantity) AS TotalQuantity FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات' GROUP BY ItemName ORDER BY TotalQuantity DESC", "required_tables": ["tbltemp_ItemsMain"], "required_columns": ["ItemName", "Quantity", "Amount", "DocumentName"], "visualization": "bar"}]}