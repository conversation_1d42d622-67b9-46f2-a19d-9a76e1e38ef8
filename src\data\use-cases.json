{"use_cases": [{"category": "تفاصيل المنتجات", "cases": [{"name": "تفاصيل منتج محدد", "query_template": "أعرض لي تفاصيل المنتج {product_name}", "sql_template": "SELECT ItemName, SUM(Quantity) as TotalSold, AVG(UnitPrice) as AvgPrice, SUM(Amount) as Revenue, COUNT(*) as TransactionCount FROM tbltemp_ItemsMain WHERE ItemName LIKE '%{product_name}%' GROUP BY ItemName", "visualization": ["table", "kpi_cards"], "analysis_points": ["إجمالي الكمية المباعة", "متوسط سعر البيع", "إجمالي الإيرادات", "<PERSON><PERSON><PERSON> المعاملات"]}, {"name": "تاريخ مبيعات منتج", "query_template": "تاريخ مبيعات {product_name}", "sql_template": "SELECT TheDate, SUM(Quantity) as DailyQuantity, SUM(Amount) as DailyRevenue FROM tbltemp_ItemsMain WHERE ItemName LIKE '%{product_name}%' GROUP BY TheDate ORDER BY TheDate", "visualization": ["line_chart", "table"], "analysis_points": ["اتجاه المبيعات عبر الزمن", "أيام الذروة", "الأنماط الموسمية"]}, {"name": "أداء منتج في الفروع", "query_template": "أداء {product_name} في الفروع", "sql_template": "SELECT BranchName, SUM(Quantity) as BranchQuantity, SUM(Amount) as BranchRevenue FROM tbltemp_ItemsMain WHERE ItemName LIKE '%{product_name}%' GROUP BY BranchName ORDER BY BranchRevenue DESC", "visualization": ["bar_chart", "pie_chart"], "analysis_points": ["أفضل الفروع أداءً", "توزيع المبيعات جغرافياً", "فرص التحسين"]}]}, {"category": "المقارنات", "cases": [{"name": "مقارنة منتجين", "query_template": "قارن بين {product1} و {product2}", "sql_template": "SELECT ItemName, SUM(Quantity) as TotalQuantity, SUM(Amount) as TotalRevenue, AVG(UnitPrice) as AvgPrice FROM tbltemp_ItemsMain WHERE ItemName IN ('{product1}', '{product2}') GROUP BY ItemName", "visualization": ["bar_chart", "comparison_table"], "analysis_points": ["الفرق في الكميات", "الفرق في الإيرادات", "مقارنة الأسعار"]}, {"name": "مقارنة الفروع", "query_template": "قارن مبيعات الفروع", "sql_template": "SELECT BranchName, SUM(Amount) as TotalSales, COUNT(*) as TransactionCount, AVG(Amount) as AvgTransaction FROM tbltemp_ItemsMain GROUP BY BranchName ORDER BY TotalSales DESC", "visualization": ["pie_chart", "bar_chart"], "analysis_points": ["أداء الفروع النسبي", "حجم المعاملات", "متوسط قيمة المعاملة"]}, {"name": "مقارنة فترات زمنية", "query_template": "قارن مبيعات {period1} مع {period2}", "sql_template": "SELECT CASE WHEN TheDate BETWEEN '{start1}' AND '{end1}' THEN '{period1}' ELSE '{period2}' END as Period, SUM(Amount) as TotalSales FROM tbltemp_ItemsMain WHERE TheDate BETWEEN '{start1}' AND '{end2}' GROUP BY CASE WHEN TheDate BETWEEN '{start1}' AND '{end1}' THEN '{period1}' ELSE '{period2}' END", "visualization": ["bar_chart", "comparison_table"], "analysis_points": ["نمو أو انخفاض المبيعات", "نسبة التغيير", "العوامل المؤثرة"]}]}, {"category": "التصنيفات والترتيب", "cases": [{"name": "أعلى منتجات مبيعاً", "query_template": "أعلى {number} منتجات مبيعاً", "sql_template": "SELECT TOP {number} ItemName, SUM(Quantity) as TotalQuantity, SUM(Amount) as TotalRevenue FROM tbltemp_ItemsMain GROUP BY ItemName ORDER BY TotalQuantity DESC", "visualization": ["bar_chart", "table"], "analysis_points": ["المنتجات الأكثر شعبية", "حصة كل منتج من السوق", "فرص التوسع"]}, {"name": "أكثر العملاء شراءً", "query_template": "أكثر {number} عملاء شراءً", "sql_template": "SELECT TOP {number} ClientName, SUM(Amount) as TotalPurchases, COUNT(*) as PurchaseCount FROM tbltemp_ItemsMain WHERE ClientName IS NOT NULL GROUP BY ClientName ORDER BY TotalPurchases DESC", "visualization": ["bar_chart", "table"], "analysis_points": ["العملاء الأكثر قيمة", "ولاء العملاء", "فرص زيادة المبيعات"]}, {"name": "أفضل الفروع أداءً", "query_template": "أفضل {number} فروع أداءً", "sql_template": "SELECT TOP {number} BranchName, SUM(Amount) as TotalSales, COUNT(*) as TransactionCount FROM tbltemp_ItemsMain GROUP BY BranchName ORDER BY TotalSales DESC", "visualization": ["bar_chart", "map_chart"], "analysis_points": ["الفروع الأكثر ربحية", "كفاءة العمليات", "استراتيجيات التوسع"]}]}, {"category": "التحليل الزمني", "cases": [{"name": "اتجاهات المبيعات الشهرية", "query_template": "اتجاهات المبيعات الشهرية", "sql_template": "SELECT YEAR(TheDate) as Year, MONTH(TheDate) as Month, SUM(Amount) as MonthlySales, SUM(Quantity) as MonthlyQuantity FROM tbltemp_ItemsMain GROUP BY YEAR(TheDate), MONTH(TheDate) ORDER BY Year, Month", "visualization": ["line_chart", "area_chart"], "analysis_points": ["النمو الشهري", "الأنماط الموسمية", "التنبؤات المستقبلية"]}, {"name": "مبيعات فترة محددة", "query_template": "مبيعات من {start_date} إلى {end_date}", "sql_template": "SELECT TheDate, SUM(Amount) as DailySales, SUM(Quantity) as DailyQuantity FROM tbltemp_ItemsMain WHERE TheDate BETWEEN '{start_date}' AND '{end_date}' GROUP BY TheDate ORDER BY TheDate", "visualization": ["line_chart", "table"], "analysis_points": ["الأداء اليومي", "أيام الذروة", "التقلبات"]}, {"name": "تحليل الأداء السنوي", "query_template": "تحليل الأداء السنوي", "sql_template": "SELECT YEAR(TheDate) as Year, SUM(Amount) as YearlySales, COUNT(DISTINCT ItemName) as ProductCount, COUNT(DISTINCT ClientName) as ClientCount FROM tbltemp_ItemsMain GROUP BY YEAR(TheDate) ORDER BY Year", "visualization": ["bar_chart", "line_chart"], "analysis_points": ["النمو السنوي", "تنوع المنتجات", "قاعدة العملاء"]}]}]}