import OpenAI from 'openai';
import fs from 'fs/promises';
import path from 'path';

// إعداد OpenRouter API
const openai = new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY || '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',
  baseURL: 'https://openrouter.ai/api/v1',
});

const MODEL_NAME = 'qwen/qwen-2.5-72b-instruct';

interface QueryAnalysis {
  success: boolean;
  intent: string;
  entities: Record<string, any>;
  tables: string[];
  columns: string[];
  visualization: string;
  confidence: number;
}

interface SQLGeneration {
  success: boolean;
  sql: string;
  explanation: string;
}

interface ResultAnalysis {
  success: boolean;
  insights: string;
  patterns: string[];
  recommendations: string[];
}

// تحميل ملفات النيات والكيانات
async function loadIntentsAndEntities() {
  try {
    const intentsPath = path.join(process.cwd(), 'src/data/intents.json');
    const entitiesPath = path.join(process.cwd(), 'src/data/entities.json');
    const useCasesPath = path.join(process.cwd(), 'src/data/use-cases.json');
    
    const [intentsData, entitiesData, useCasesData] = await Promise.all([
      fs.readFile(intentsPath, 'utf-8'),
      fs.readFile(entitiesPath, 'utf-8'),
      fs.readFile(useCasesPath, 'utf-8')
    ]);

    return {
      intents: JSON.parse(intentsData),
      entities: JSON.parse(entitiesData),
      useCases: JSON.parse(useCasesData)
    };
  } catch (error) {
    console.error('خطأ في تحميل ملفات النيات والكيانات:', error);
    return null;
  }
}

// تحميل مخطط قاعدة البيانات
async function loadDatabaseSchema() {
  try {
    const schemaPath = path.join(process.cwd(), 'src/data/database-schema.json');
    const schemaData = await fs.readFile(schemaPath, 'utf-8');
    return JSON.parse(schemaData);
  } catch (error) {
    console.error('خطأ في تحميل مخطط قاعدة البيانات:', error);
    return null;
  }
}

export async function analyzeQuery(query: string): Promise<QueryAnalysis> {
  try {
    const data = await loadIntentsAndEntities();
    const schema = await loadDatabaseSchema();
    
    if (!data || !schema) {
      return {
        success: false,
        intent: '',
        entities: {},
        tables: [],
        columns: [],
        visualization: 'table',
        confidence: 0
      };
    }

    const prompt = `
أنت محلل ذكي للاستعلامات باللغة العربية. مهمتك تحليل الاستعلام التالي واستخراج النية والكيانات.

الاستعلام: "${query}"

النيات المتاحة:
${JSON.stringify(data.intents.intents, null, 2)}

الكيانات المتاحة:
${JSON.stringify(data.entities.entities, null, 2)}

مخطط قاعدة البيانات:
${JSON.stringify(schema, null, 2)}

يرجى تحليل الاستعلام وإرجاع النتيجة بالتنسيق التالي:
{
  "success": true,
  "intent": "اسم النية المناسبة",
  "entities": {
    "entity_name": "القيمة المستخرجة"
  },
  "tables": ["أسماء الجداول المطلوبة"],
  "columns": ["أسماء الأعمدة المطلوبة"],
  "visualization": "نوع التصور المناسب (table, bar, line, pie)",
  "confidence": 0.95
}
`;

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت محلل ذكي للاستعلامات باللغة العربية. تحلل الاستعلامات وتستخرج النيات والكيانات بدقة.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1000
    });

    const result = response.choices[0]?.message?.content;
    if (!result) {
      throw new Error('لم يتم الحصول على رد من النموذج اللغوي');
    }

    // محاولة تحليل الرد كـ JSON
    try {
      const analysis = JSON.parse(result);
      return analysis;
    } catch (parseError) {
      console.error('خطأ في تحليل رد النموذج اللغوي:', parseError);
      return {
        success: false,
        intent: '',
        entities: {},
        tables: [],
        columns: [],
        visualization: 'table',
        confidence: 0
      };
    }

  } catch (error) {
    console.error('خطأ في تحليل الاستعلام:', error);
    return {
      success: false,
      intent: '',
      entities: {},
      tables: [],
      columns: [],
      visualization: 'table',
      confidence: 0
    };
  }
}

export async function generateSQL(queryAnalysis: QueryAnalysis): Promise<SQLGeneration> {
  try {
    const schema = await loadDatabaseSchema();
    const useCases = await loadIntentsAndEntities();
    
    if (!schema || !useCases) {
      return {
        success: false,
        sql: '',
        explanation: 'فشل في تحميل البيانات المطلوبة'
      };
    }

    const prompt = `
أنت خبير في كتابة استعلامات SQL Server. مهمتك إنشاء استعلام SQL دقيق وفعال.

تحليل الاستعلام:
${JSON.stringify(queryAnalysis, null, 2)}

مخطط قاعدة البيانات:
${JSON.stringify(schema, null, 2)}

أمثلة حالات الاستخدام:
${JSON.stringify(useCases?.useCases, null, 2)}

يرجى إنشاء استعلام SQL مناسب وإرجاع النتيجة بالتنسيق التالي:
{
  "success": true,
  "sql": "استعلام SQL كامل وصحيح",
  "explanation": "شرح مفصل للاستعلام باللغة العربية"
}

ملاحظات مهمة:
- استخدم أسماء الجداول والأعمدة الصحيحة من المخطط
- تأكد من صحة صيغة SQL Server
- استخدم الفلاتر والشروط المناسبة
- أضف ORDER BY و TOP إذا كان مناسباً
- استخدم GROUP BY للتجميع عند الحاجة
`;

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت خبير في SQL Server وتكتب استعلامات دقيقة وفعالة باللغة العربية.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1500
    });

    const result = response.choices[0]?.message?.content;
    if (!result) {
      throw new Error('لم يتم الحصول على رد من النموذج اللغوي');
    }

    try {
      const sqlGeneration = JSON.parse(result);
      return sqlGeneration;
    } catch (parseError) {
      console.error('خطأ في تحليل رد النموذج اللغوي:', parseError);
      return {
        success: false,
        sql: '',
        explanation: 'فشل في تحليل رد النموذج اللغوي'
      };
    }

  } catch (error) {
    console.error('خطأ في توليد SQL:', error);
    return {
      success: false,
      sql: '',
      explanation: 'فشل في توليد استعلام SQL'
    };
  }
}

export async function analyzeResults(originalQuery: string, data: any[], queryAnalysis: QueryAnalysis): Promise<ResultAnalysis> {
  try {
    const prompt = `
أنت محلل بيانات خبير. مهمتك تحليل نتائج الاستعلام وتقديم رؤى ذكية.

الاستعلام الأصلي: "${originalQuery}"

تحليل الاستعلام:
${JSON.stringify(queryAnalysis, null, 2)}

البيانات (أول 10 صفوف):
${JSON.stringify(data.slice(0, 10), null, 2)}

إجمالي عدد الصفوف: ${data.length}

يرجى تحليل البيانات وإرجاع النتيجة بالتنسيق التالي:
{
  "success": true,
  "insights": "تحليل مفصل وذكي للنتائج باللغة العربية",
  "patterns": ["نمط 1", "نمط 2", "نمط 3"],
  "recommendations": ["توصية 1", "توصية 2", "توصية 3"]
}

يجب أن يتضمن التحليل:
- ملخص للنتائج الرئيسية
- الأنماط والاتجاهات المكتشفة
- المقارنات والإحصائيات المهمة
- التوصيات العملية
- رؤى قابلة للتنفيذ
`;

    const response = await openai.chat.completions.create({
      model: MODEL_NAME,
      messages: [
        {
          role: 'system',
          content: 'أنت محلل بيانات خبير تقدم رؤى ذكية وتوصيات عملية باللغة العربية.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 2000
    });

    const result = response.choices[0]?.message?.content;
    if (!result) {
      throw new Error('لم يتم الحصول على رد من النموذج اللغوي');
    }

    try {
      const analysis = JSON.parse(result);
      return analysis;
    } catch (parseError) {
      console.error('خطأ في تحليل رد النموذج اللغوي:', parseError);
      return {
        success: true,
        insights: result, // استخدام النص كما هو إذا فشل التحليل
        patterns: [],
        recommendations: []
      };
    }

  } catch (error) {
    console.error('خطأ في تحليل النتائج:', error);
    return {
      success: false,
      insights: 'فشل في تحليل النتائج',
      patterns: [],
      recommendations: []
    };
  }
}
